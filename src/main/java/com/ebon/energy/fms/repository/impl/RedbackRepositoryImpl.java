package com.ebon.energy.fms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.ConfigurationType;
import com.ebon.energy.fms.common.enums.FirmwareVersion;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.ACCoupledUtility;
import com.ebon.energy.fms.common.utils.BatteryEnergyStatics;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.common.utils.WASafetyCountryUtility;
import com.ebon.energy.fms.domain.entity.ConfigurationsDO;
import com.ebon.energy.fms.domain.entity.DeviceControl;
import com.ebon.energy.fms.domain.vo.ElectricalConfiguration;
import com.ebon.energy.fms.domain.vo.ElectricalControllerDataDto;
import com.ebon.energy.fms.domain.vo.product.control.DeviceControlDTO;
import com.ebon.energy.fms.domain.vo.product.control.ProductDTO;
import com.ebon.energy.fms.domain.vo.product.control.ProductInfo;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.product.control.UpdateDeviceControlDTO;
import com.ebon.energy.fms.domain.vo.setting.UpdateElectricalConfigurationDTO;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.ConfigurationsMapper;
import com.ebon.energy.fms.mapper.third.ProductControlMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.repository.RedbackRepository;

import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.SystemStatusHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

/**
 * Implementation of the RedbackRepository interface
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RedbackRepositoryImpl implements RedbackRepository {

    private final ObjectMapper objectMapper;

    private final ProductMapper productMapper;

    private final ProductControlMapper productControlMapper;
    private final ConfigurationsMapper configurationsMapper;

    /**
     * Convert system status JSON to SystemStatus object
     *
     * @param systemStatusJson The system status JSON
     * @return The SystemStatus object
     */
    private SystemStatus convertSystemStatus(String systemStatusJson) {
        try {
            return objectMapper.readValue(systemStatusJson, SystemStatus.class);
        } catch (Exception e) {
            log.error("Error converting system status JSON: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Get product information as a ProductDTO
     *
     * @param serialNumber Product serial number
     * @return ProductDTO containing product information
     */
    @Override
    public ProductDTO getProductDTO(String serialNumber) {

        try {
            log.info("Getting product DTO  and serial number {}", serialNumber);

            // Get product information
            ProductInfo product = productMapper.getProductInfo(serialNumber);
            if (product == null) {
                throw new RuntimeException("The serial number doesn't exist.");
            }
            // Parse system status if available
            SystemStatus systemStatus = null;

            if (product.getLatestSystemStatus() != null && !product.getLatestSystemStatus().isEmpty()) {
                // Convert the JSON string to SystemStatusDto object
                // log.info("JSON:{}", product.getLatestSystemStatus());
                systemStatus = JSONObject.parseObject(product.getLatestSystemStatus(), SystemStatus.class);
            }

            // Create and return the ProductDTO
            ProductDTO productDTO = new ProductDTO(product, systemStatus);

            log.info("Successfully retrieved product DTO for serial number: {}", serialNumber);
            return productDTO;
        } catch (Exception e) {
            log.error("Error getting product DTO  and serial number {}: {}", serialNumber, e.getMessage(), e);
            throw new RuntimeException("Error getting product information: " + e.getMessage(), e);
        }
    }

    @Override
    public com.ebon.energy.fms.domain.vo.product.control.DeviceControlDTO getDeviceControl(String serialNumber) {
        try {
            ProductInfo product = productMapper.getProductInfo(serialNumber);
            if (product == null) {
                throw new RuntimeException("The serial number doesn't exist.");
            }
            String cloudConfigJson = product.getCloudConfiguration();
            DeviceControl cloudConfig = objectMapper.readValue(cloudConfigJson, DeviceControl.class);
            if (cloudConfig != null) {
                if (cloudConfig.getDateTimeUpdated() == null) {
                    SimpleDateFormat sdf = new SimpleDateFormat(DeviceControl.LOCAL_DATE_TIME_FORMAT);
                    cloudConfig.setDateTimeUpdated(sdf.format(new Date()));
                }

                cloudConfig.setDeviceSerialNumber(serialNumber);
            }
            DeviceControlDTO dto = new DeviceControlDTO();
            dto.setDeviceControl(cloudConfig);
            dto.setInverterTimeZoneId(product.getInverterTimeZone());
            return dto;
        } catch (Exception e) {
            log.error("Error getting device control serial number {}: {}", serialNumber, e.getMessage(), e);
            throw new RuntimeException("Error retrieving device control data", e);
        }
    }

    @Override
    public void updateDeviceControlRoss1(UpdateDeviceControlDTO updateDeviceControlDTO) {
        var product = productControlMapper.getProductBySerial(updateDeviceControlDTO.getSerialNumber());
        if (product == null) {
            throw new BizException("The serial number doesn't exist.");
        }
        validateConfigurationOrThrow(updateDeviceControlDTO);

        var isOptimised = product.canBeOptimised() && product.getIsOptimised();

        var isFromWebJob = false;

        var isFromDevice = false;
        if (isOptimised) {
            var shouldDisableOptimisation = updateDeviceControlDTO.isFromPortal();
            var shouldOverrideOptimisation = isFromWebJob || isFromDevice;
            var shouldAcceptDeviceControl = shouldDisableOptimisation || shouldOverrideOptimisation;
            if (!shouldAcceptDeviceControl) {
                // var errorMessage = isOwner
                // ? "Please disable optimisation through the portal before updating the device
                // control."
                // : "You don't have access to disable optimisation";
                // throw new UnauthorizedAccessException(errorMessage);
            }
            if (shouldDisableOptimisation) {
                // // must be a home owner to disable optimisation
                // if (!isOwner)
                // {
                // throw new UnauthorizedAccessException("You don't have access to disable
                // optimisation");
                // }
                productControlMapper.updateProductOptimizationStatus(updateDeviceControlDTO.getSerialNumber(), false);
            }
        }

        if (!isFromDevice && !isFromWebJob) {
            // must have RW on Device Control to udpate device control (unless you are
            // consumer or web job)
            // if (!hasRWDeviceControlAccess)
            // {
            // throw new UnauthorizedAccessException("You don't have access to update device
            // control of this system.");
            // }
        }
        handleConfiguration(updateDeviceControlDTO,
                configurationsMapper.findByRedbackProductSn(updateDeviceControlDTO.getSerialNumber()), isFromDevice);
    }

    @Override
    public SystemStatus getLatestSystemStatusAsync(String serialNumber) {
        var productInfo = productMapper.getProductInfo(serialNumber);
        if (productInfo.getLatestSystemStatus() == null) {
            return null;
        } else {
            return JSONObject.parseObject(productInfo.getLatestSystemStatus(), SystemStatus.class);
        }
    }

    @Override
    public ElectricalControllerDataDto getElectricalControllerDataRoss1(String serialNumber) {
        try {
            // 获取电气配置数据
            var electricalConfigurationDto = productMapper.selectElectricalConfiguration(serialNumber);
            if (electricalConfigurationDto == null) {
                throw new RuntimeException("No electrical configuration found for serial number: " + serialNumber);
            }

            // 反序列化云端配置和设备配置
            ElectricalConfiguration cloudConfig = null;
            ElectricalConfiguration deviceConfig = null;

            if (electricalConfigurationDto.getCloud() != null) {
                cloudConfig = objectMapper.readValue(electricalConfigurationDto.getCloud(),
                        ElectricalConfiguration.class);
            }

            if (electricalConfigurationDto.getDevice() != null) {
                deviceConfig = objectMapper.readValue(electricalConfigurationDto.getDevice(),
                        ElectricalConfiguration.class);
            }

            // 获取最新系统状态
            var systemStatus = getLatestSystemStatusAsync(serialNumber);

            // 获取固件版本和ROSS版本
            FirmwareVersion firmwareVersion = null;
            if (systemStatus != null && systemStatus.getInverter() != null
                    && systemStatus.getInverter().getFirmwareVersion() != null) {
                try {
                    firmwareVersion = com.ebon.energy.fms.common.enums.FirmwareVersion.fromVersionString(systemStatus.getInverter().getFirmwareVersion());

                } catch (Exception e) {
                    log.warn("Failed to parse firmware version: {}", systemStatus.getInverter().getFirmwareVersion());
                }
            }

            RossVersion rossVersion = null;
            if (systemStatus != null && systemStatus.getOuijaBoard() != null
                    && systemStatus.getOuijaBoard().getSoftwareVersion() != null) {
                rossVersion = new RossVersion(systemStatus.getOuijaBoard().getSoftwareVersion());
            }

            // 判断是否支持AC耦合和WA安全国家
            boolean isACCoupledSupported = ACCoupledUtility.isACCoupledSupported(deviceConfig, firmwareVersion);
            boolean isWASafetyCountrySupported = WASafetyCountryUtility.isWASafetyCountrySupported(firmwareVersion, rossVersion);

            // 修补遗留电池信息
            if (cloudConfig != null) {
                cloudConfig.patchLegacyBatteryInfo();
            }

            // 构建返回对象
            ElectricalControllerDataDto result = new ElectricalControllerDataDto();
            result.setElectricalConfiguration(cloudConfig);
            result.setIsReadOnly(false); // TODO: 需要根据实际访问权限设置
            result.setIsInSync(cloudConfig != null && cloudConfig.equals(deviceConfig));
            result.setCloudTimestamp(cloudConfig != null ? cloudConfig.getDateTimeUpdated() : null);
            result.setDeviceTimestamp(deviceConfig != null ? deviceConfig.getDateTimeUpdated() : null);
            result.setIsACCoupledSupported(isACCoupledSupported);
            result.setIsWASafetyCountrySupported(isWASafetyCountrySupported);
            result.setIsGridOn(SystemStatusHelper.isOnGrid(systemStatus));
            result.setProductModelName(systemStatus != null && systemStatus.getInverter() != null
                    ? systemStatus.getInverter().getModelName()
                    : null);

            return result;
        } catch (Exception e) {
            log.error("Error getting electrical controller data for serial number {}: {}", serialNumber, e.getMessage(),
                    e);
            throw new RuntimeException("Error retrieving electrical controller data", e);
        }
    }

    @Override
    public void updateElectricalSettingRoss1(UpdateElectricalConfigurationDTO updateElectricalConfigurationDto) {

    }


    private void validateConfigurationOrThrow(UpdateDeviceControlDTO updateDeviceControlDTO) {
        var error = updateDeviceControlDTO.getDeviceControl().validate();
        if (StringUtils.hasLength(error)) {
            throw new BizException("Error updating device control: " + error);
        }
    }

    @SneakyThrows
    private void handleConfiguration(UpdateDeviceControlDTO updateDeviceControlDto, ConfigurationsDO configuration,
            Boolean isFromDevice) {
        if (configuration != null) {
            if (isFromDevice) {
                // TODO
            } else {
                updateDeviceControlDto.getDeviceControl().setGroupEventId(null);
                var deviceControl = objectMapper.writeValueAsString(updateDeviceControlDto.getDeviceControl());
                configuration.setConfigurations(deviceControl);
            }
            configuration.setModifiedDateTime(Timestamp.from(Instant.now()));
            configuration.setLastModifiedById(RequestUtil.getLoginUserEmail());
            configurationsMapper.updateById(configuration);
        } else {
            var deviceControl = objectMapper.writeValueAsString(updateDeviceControlDto.getDeviceControl());
            var configurationsDO = new ConfigurationsDO();
            configurationsDO.setRedbackProductSn(updateDeviceControlDto.getSerialNumber());
            configurationsDO.setConfigurationType(ConfigurationType.DeviceControl.getValue());
            configurationsDO.setConfigurations(deviceControl);
            configurationsDO.setConfigurationsOnDevice(isFromDevice ? deviceControl : "{}");
            configurationsDO.setModifiedDateTime(Timestamp.from(Instant.now()));
            configurationsDO.setLastModifiedById(RequestUtil.getLoginUserEmail());
            configurationsMapper.insert(configurationsDO);
        }
    }

}
